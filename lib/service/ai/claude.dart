import 'dart:async';

import 'package:dasso_reader/service/ai/ai_dio.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'dart:convert';

Stream<String> claudeGenerateStream(
  List<Map<String, dynamic>> messages,
  Map<String, String> config,
) async* {
  final url = config['url'];
  final apiKey = config['api_key'];
  final model = config['model'];
  final dio = AiDio.instance.dio;

  try {
    final response = await dio.post(
      url!,
      options: Options(
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': apiKey,
          'anthropic-version': '2023-06-01',
        },
        responseType: ResponseType.stream,
        validateStatus: (status) => true,
      ),
      data: {
        'model': model,
        'max_tokens': 2048,
        'messages': messages,
        'stream': true,
      },
    );

    final stream = response.data.stream;
    // Safe stream processing with proper type handling
    final typedStream =
        stream as Stream<Uint8List>? ?? Stream<Uint8List>.empty();
    final stringStream = typedStream.transform(
      StreamTransformer<Uint8List, String>.fromHandlers(
        handleData: (Uint8List data, EventSink<String> sink) {
          sink.add(utf8.decode(data));
        },
      ),
    );

    await for (final chunk in stringStream) {
      if (response.statusCode != 200) {
        yield* Stream.error('error ${response.statusCode} \n $chunk');
        continue;
      }

      // Safe iteration over lines with proper type checking
      final chunkString = chunk as String? ?? '';
      final lines = chunkString.split('\n');
      for (final line in lines) {
        if (line.isEmpty || line.startsWith('event: ')) continue;
        final data = line.startsWith('data: ') ? line.substring(6) : line;
        try {
          final json = jsonDecode(data) as Map<String, dynamic>;

          if (json['type'] == 'content_block_delta') {
            final delta = json['delta'] as Map<String, dynamic>?;
            final text = delta?['text'] as String?;
            if (text != null && text.isNotEmpty) {
              yield text; // Now properly typed as String
            }
          }
        } catch (e) {
          yield* Stream.error('Parse error: $e\nData: $data');
          continue;
        }
      }
    }
  } catch (e) {
    if (kDebugMode) {
      throw Exception(e);
    } else {
      yield* Stream.error('Request failed: $e');
    }
  } finally {
    dio.close();
  }
}
