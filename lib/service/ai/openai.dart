import 'dart:async';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:dasso_reader/service/ai/ai_dio.dart';
import 'dart:convert';

Stream<String> openAiGenerateStream(
  List<Map<String, dynamic>> messages,
  Map<String, String> config,
) async* {
  final url = config['url'];
  final apiKey = config['api_key'];
  final model = config['model'];
  final dio = AiDio.instance.dio;

  try {
    // Configure timeout at the Dio instance level for better error handling
    dio.options.connectTimeout = const Duration(seconds: 30);
    dio.options.receiveTimeout = const Duration(seconds: 60);
    dio.options.sendTimeout = const Duration(seconds: 30);

    final response = await dio.post(
      url!,
      options: Options(
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        responseType: ResponseType.stream,
        validateStatus: (status) => true,
      ),
      data: {
        'model': model,
        'messages': messages,
        'stream': true,
      },
    );

    final stream = response.data.stream;
    // Safe stream processing with proper type handling
    final typedStream =
        stream as Stream<Uint8List>? ?? Stream<Uint8List>.empty();
    final stringStream = typedStream.transform(
      StreamTransformer<Uint8List, String>.fromHandlers(
        handleData: (Uint8List data, EventSink<String> sink) {
          sink.add(utf8.decode(data));
        },
      ),
    );

    await for (final chunk in stringStream) {
      if (response.statusCode != 200) {
        yield* Stream.error('error ${response.statusCode} \n $chunk');
        continue;
      }

      // Safe iteration with proper type checking
      final chunkString = chunk as String? ?? '';
      final lines = chunkString.split('\n');
      for (final line in lines) {
        if (line.trim().isEmpty) continue;

        if (line.startsWith('data: ')) {
          final data = line.substring(6);
          if (data.trim() == '[DONE]') break;

          try {
            final json = jsonDecode(data) as Map<String, dynamic>;
            final choices = json['choices'] as List<dynamic>?;
            final firstChoice = choices?.first as Map<String, dynamic>?;
            final delta = firstChoice?['delta'] as Map<String, dynamic>?;
            final content = delta?['content'] as String?;
            if (content != null) {
              yield content; // Now properly typed as String
            }
          } catch (e) {
            yield* Stream.error('Parse error: $e\nData: $data');
            continue;
          }
        }
      }
    }
  } catch (e) {
    yield* Stream.error('Request failed: $e');
  } finally {
    dio.close();
  }
}
